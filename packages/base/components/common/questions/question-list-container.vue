<script setup lang="ts">
import QsProvide from './qs-provide/index.vue'
import QuestionItemContainer from './question-item-container.vue'

defineOptions({
  name: 'QuestionListContainer',
})
const props = defineProps<Props>()
interface Props {
  type?: 'edit' | 'answer' | 'preview'
}
const questionList = defineModel<Question.ProcessedQuestionData[]>('questionList', {
  default: () => [],
})
</script>

<template>
  <QsProvide :type="props.type">
    <template v-for="(item, index) in questionList" :key="item.id">
      <QuestionItemContainer v-model="questionList[index]" :item-info="item" />
    </template>
  </QsProvide>
</template>
